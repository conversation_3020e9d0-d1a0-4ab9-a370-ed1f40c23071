// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenu = document.querySelector('.mobile-menu');
    const menuBtn = document.querySelector('.menu-btn');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-links a');
    const mobileContactBtn = document.querySelector('.mobile-contact .contact-btn');
    const body = document.body;

    // Toggle mobile menu
    menuBtn.addEventListener('click', function() {
        mobileMenu.classList.toggle('active');
        menuBtn.classList.toggle('active');

        // Prevent body scroll when menu is open
        if (mobileMenu.classList.contains('active')) {
            body.style.overflow = 'hidden';
        } else {
            body.style.overflow = '';
        }
    });

    // Close mobile menu when clicking on nav links
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
            menuBtn.classList.remove('active');
            body.style.overflow = '';
        });
    });

    // Close mobile menu when clicking on contact button
    if (mobileContactBtn) {
        mobileContactBtn.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
            menuBtn.classList.remove('active');
            body.style.overflow = '';
        });
    }

    // Close mobile menu when clicking outside
    mobileMenu.addEventListener('click', function(e) {
        if (e.target === mobileMenu) {
            mobileMenu.classList.remove('active');
            menuBtn.classList.remove('active');
            body.style.overflow = '';
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            mobileMenu.classList.remove('active');
            menuBtn.classList.remove('active');
            body.style.overflow = '';
        }
    });
});
