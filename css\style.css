/* font-family: 'Poppins', sans-serif; */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
html{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
    scroll-behavior: smooth;
}
.container{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem 2rem; 

}
.navbar{
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    padding: 0rem 2rem;
    background-color: #fff;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1px solid #eee;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.logo{
    width: 150px;
    height: 100px;  
      
}
.logo img{
    width: 100%;
    height: 100%;
    object-fit: contain;
}
.nav-links{
    display: flex;
    justify-content: space-between;
    align-items: center;
    list-style: none;
    padding: 0 1rem;
    margin: 0;
}
.nav-links li{
    margin: 0 1rem;
}
.nav-links li a{
    text-decoration: none;
    color: #000;
    font-size: 1.2rem;
    font-weight: 500;
    text-transform: uppercase;
}
.nav-links li a:hover{
    color: #9e0ba2;
    text-decoration: none;
    transition: all 0.3s ease;
}
.contact{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.contact-btn{
    text-decoration: none;
    color: #fff;
    background-color: #000;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-size: 1.2rem;
    font-weight: 500;
    text-transform: uppercase;
}
.contact-btn:hover{
    color: #fff;
    background-color: #9e0ba2;
    transition: all 0.3s ease;
    
}
.mobile-menu{
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}
.mobile-menu .logo{
    width: 100px;
    height: 100px;
    margin: 1rem auto;
}
.mobile-menu .logo img{
    width: 100%;
    height: 100%;
    object-fit: contain;
}
.menu-btn{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    width: 30px;
}
.menu-btn .bar{
    width: 100%;
    height: 3px;
    background-color: #000;
    border-radius: 5px;
}
@media (max-width: 768px) {
    .navbar{
        flex-direction: column;
        align-items: flex-start;
        padding: 1rem 0;
        height: 100vh;
        overflow-y: auto;
    }
    .nav-links{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
        padding: 0;
        margin: 0;
    }
    .nav-links li{
        margin: 0.5rem 0;
    }
    .contact{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
        padding: 0;
        margin: 0;
    }
    .contact-btn{
        margin: 1rem 0;
    }
    .mobile-menu{
        display: block;
    }
}
.mobile-menu.active{
    opacity: 1;
    visibility: visible;
}