/* font-family: 'Poppins', sans-serif; */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-family: 'Poppins', sans-serif;
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
}

.container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.5rem 2rem;
    background-color: #fff;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1px solid #eee;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logo {
    width: 120px;
    height: 60px;
    flex-shrink: 0;
}

.logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.nav-links {
    display: flex;
    justify-content: center;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 2rem;
}

.nav-links li {
    margin: 0;
}

.nav-links li a {
    text-decoration: none;
    color: #000;
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
    position: relative;
}

.nav-links li a:hover {
    color: #9e0ba2;
}

.nav-links li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #9e0ba2;
    transition: width 0.3s ease;
}

.nav-links li a:hover::after {
    width: 100%;
}

.contact {
    display: flex;
    align-items: center;
}

.contact-btn {
    text-decoration: none;
    color: #fff;
    background-color: #000;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.contact-btn:hover {
    color: #fff;
    background-color: #9e0ba2;
    transform: translateY(-2px);
}

/* Hamburger Menu Button */
.menu-btn {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1001;
}

.menu-btn .bar {
    width: 100%;
    height: 3px;
    background-color: #000;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.menu-btn.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.menu-btn.active .bar:nth-child(2) {
    opacity: 0;
}

.menu-btn.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu */
.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: #fff;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding-top: 80px;
}

.mobile-menu.active {
    opacity: 1;
    visibility: visible;
}

.mobile-nav-links {
    display: flex;
    flex-direction: column;
    align-items: center;
    list-style: none;
    padding: 1rem 0;
    margin: 0;
    gap: 2rem;
}

.mobile-nav-links li a {
    text-decoration: none;
    color: #000;
    font-size: 1.5rem;
    font-weight: 500;
    text-transform: uppercase;
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.mobile-nav-links li a:hover {
    color: #9e0ba2;
}

.mobile-contact {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.mobile-contact .contact-btn {
    font-size: 1.2rem;
    padding: 1rem 2rem;
}
/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0.6rem 1.5rem;
    }

    .navbar {
        padding: 0.5rem 1.5rem;
    }

    .nav-links {
        gap: 1.5rem;
    }

    .nav-links li a {
        font-size: 0.9rem;
    }

    .contact-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.8rem;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .container {
        padding: 0.5rem 1rem;
    }

    .navbar {
        padding: 0.5rem 1rem;
        height: 70px;
    }

    .logo {
        width: 100px;
        height: 50px;
    }

    .nav-links {
        display: none;
    }

    .contact {
        display: none;
    }

    .menu-btn {
        display: flex;
    }

    .mobile-menu {
        display: block;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .navbar {
        padding: 0.5rem 0.75rem;
        height: 60px;
    }

    .logo {
        width: 80px;
        height: 50px;
    }

    .menu-btn {
        width: 25px;
        height: 18px;
    }

    .mobile-nav-links li a {
        font-size: 1.3rem;
    }

    .mobile-contact .contact-btn {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
    }
}

/* Large Desktop Styles */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
        padding: 0.6rem 3rem;
    }

    .navbar {
        padding: 0.5rem 3rem;
    }

    .nav-links {
        gap: 2.5rem;
    }

    .nav-links li a {
        font-size: 1.1rem;
    }

    .contact-btn {
        padding: 0.8rem 1.8rem;
        font-size: 1rem;
    }
}

/* Hero Section Styles */
.hero {
    margin-top: 80px; /* Account for fixed navbar */
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    padding: 2rem;
}

.hero-content {
    text-align: center;
    max-width: 600px;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 1rem;
}

.brand-name {
    color: #9e0ba2;
    font-weight: 700;
}

.reverse-i {
    display: inline-block;
    transform: rotate(180deg);
    color: #9e0ba2;
}

.hero-content p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn {
    display: inline-block;
    text-decoration: none;
    color: #fff;
    background-color: #9e0ba2;
    padding: 1rem 2rem;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: 500;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.btn:hover {
    background-color: #000;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .hero {
        margin-top: 70px;
        padding: 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .btn {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero {
        margin-top: 60px;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }
}